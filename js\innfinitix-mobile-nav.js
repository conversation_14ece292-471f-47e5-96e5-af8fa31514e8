/*!
 * Kulfi Printex Mobile Navigation Enhancement
 * Ensures mobile navigation works properly across all devices
 * Author: Kulfi Printex Development Team
 * Version: 1.0
 */

(function () {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function () {

        // Mobile Navigation Enhancement
        function initMobileNavigation() {
            const triggers = document.querySelectorAll('.primary-menu-trigger');
            const menuContainers = document.querySelectorAll('.menu-container');
            const body = document.body;

            triggers.forEach(function (trigger) {
                trigger.addEventListener('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Toggle active state
                    trigger.classList.toggle('primary-menu-trigger-active');

                    // Update aria-expanded for accessibility
                    const button = trigger.querySelector('.cnvs-hamburger');
                    if (button) {
                        const isExpanded = trigger.classList.contains('primary-menu-trigger-active');
                        button.setAttribute('aria-expanded', isExpanded);
                    }

                    // Toggle menu containers
                    menuContainers.forEach(function (container) {
                        container.classList.toggle('d-block');
                    });

                    // Toggle body class
                    body.classList.toggle('primary-menu-open');
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', function (e) {
                if (!e.target.closest('.primary-menu-trigger') &&
                    !e.target.closest('.primary-menu') &&
                    !e.target.closest('.menu-container')) {

                    // Close all menus
                    triggers.forEach(function (trigger) {
                        trigger.classList.remove('primary-menu-trigger-active');
                        const button = trigger.querySelector('.cnvs-hamburger');
                        if (button) {
                            button.setAttribute('aria-expanded', 'false');
                        }
                    });

                    menuContainers.forEach(function (container) {
                        container.classList.remove('d-block');
                    });

                    body.classList.remove('primary-menu-open');
                }
            });

            // Handle escape key
            document.addEventListener('keydown', function (e) {
                if (e.key === 'Escape' && body.classList.contains('primary-menu-open')) {
                    triggers.forEach(function (trigger) {
                        trigger.classList.remove('primary-menu-trigger-active');
                        const button = trigger.querySelector('.cnvs-hamburger');
                        if (button) {
                            button.setAttribute('aria-expanded', 'false');
                        }
                    });

                    menuContainers.forEach(function (container) {
                        container.classList.remove('d-block');
                    });

                    body.classList.remove('primary-menu-open');
                }
            });

            // Handle window resize
            window.addEventListener('resize', function () {
                if (window.innerWidth > 991) {
                    // Close mobile menu on desktop
                    triggers.forEach(function (trigger) {
                        trigger.classList.remove('primary-menu-trigger-active');
                        const button = trigger.querySelector('.cnvs-hamburger');
                        if (button) {
                            button.setAttribute('aria-expanded', 'false');
                        }
                    });

                    menuContainers.forEach(function (container) {
                        container.classList.remove('d-block');
                    });

                    body.classList.remove('primary-menu-open');
                }
            });

            // Sub-menu toggle functionality
            const subMenuTriggers = document.querySelectorAll('.sub-menu-trigger');
            subMenuTriggers.forEach(function (trigger) {
                trigger.addEventListener('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const subMenu = trigger.parentNode.querySelector('.sub-menu-container');
                    if (subMenu) {
                        subMenu.classList.toggle('d-block');
                        trigger.classList.toggle('active');
                    }
                });
            });
        }

        // Initialize mobile navigation
        initMobileNavigation();

        // Ensure menu works with existing Canvas framework
        if (window.__core && window.__core.getVars) {
            // Re-initialize if Canvas framework is present
            setTimeout(initMobileNavigation, 100);
        }
    });

    // Fallback for older browsers
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', arguments.callee);
    } else {
        // DOM is already ready
        arguments.callee();
    }

})();
