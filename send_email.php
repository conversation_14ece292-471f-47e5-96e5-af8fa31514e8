

<?php

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

require 'vendor/autoload.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Collect and sanitize form data
    $firstname = htmlspecialchars(trim($_POST['widget-subscribe-form-firstname']));
    $lastname = htmlspecialchars(trim($_POST['widget-subscribe-form-lastname']));
    $email = htmlspecialchars(trim($_POST['widget-subscribe-form-email']));
    $mobile = htmlspecialchars(trim($_POST['widget-subscribe-form-number']));
    $printerModel = htmlspecialchars(trim($_POST['widget-subscribe-form-printermodel']));
    $agree = isset($_POST['agree1']) ? true : false;

    // Validate form data
    if (empty($firstname) || empty($lastname) || empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL) || empty($mobile) || empty($printerModel) || !$agree) {
        // Return error response in JSON format
        echo json_encode(['success' => false, 'message' => 'Please fill all fields correctly and accept the terms.']);
        exit;
    }

    $mail = new PHPMailer(true);

    try {
        // Mail server settings
        $mail->isSMTP();
        $mail->Host = 'smtp.gmail.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = 'wdjhnwoaighmrfhn';
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = 587;

        // Recipients
        $mail->setFrom('<EMAIL>', 'Kulfi Printex');
        $mail->addAddress('<EMAIL>');

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'New Subscription from ' . $firstname . ' ' . $lastname;
        $mail->Body = "<h3>New Subscriber Details:</h3>
                       <p><strong>First Name:</strong> $firstname</p>
                       <p><strong>Last Name:</strong> $lastname</p>
                       <p><strong>Email:</strong> $email</p>
                       <p><strong>Mobile Number:</strong> $mobile</p>
                       <p><strong>Printer Model:</strong> $printerModel</p>";

        $mail->send();

        echo json_encode(['success' => true, 'message' => 'Form submitted successfully!']);
    } catch (Exception $e) {
        // Return error response in JSON format
        echo json_encode(['success' => false, 'message' => "Mailer Error: {$mail->ErrorInfo}"]);
    }
} else {
    // Invalid request method
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
}
