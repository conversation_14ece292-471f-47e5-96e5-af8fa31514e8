<?php
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

require 'vendor/autoload.php'; // Include the PHPMailer autoload file

$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate the form inputs
    if (empty($_POST['template-contactform-name']) || empty($_POST['template-contactform-email']) || empty($_POST['template-contactform-message'])) {
        throw new Exception('Please fill in all required fields.');
    }

    $name = htmlspecialchars(strip_tags(trim($_POST['template-contactform-name'])));
    $email = htmlspecialchars(strip_tags(trim($_POST['template-contactform-email'])));
    $phone = htmlspecialchars(strip_tags(trim($_POST['template-contactform-phone'])));
    $subject = htmlspecialchars(strip_tags(trim($_POST['template-contactform-subject'])));
    $message = htmlspecialchars(strip_tags(trim($_POST['template-contactform-message'])));

    // Initialize PHPMailer
    $mail = new PHPMailer(true);

    // Set up the mailer
    $mail->isSMTP();
    $mail->Host = 'smtp.gmail.com';
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>';
    $mail->Password = 'wdjhnwoaighmrfhn';
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = 587;

    // Recipients
    $mail->setFrom('<EMAIL>', 'Kulfi Printex');
    $mail->addAddress('<EMAIL>');

    $mail->isHTML(true);
    $mail->Subject = 'New Contact Form Submission - ' . $subject;
    $mail->Body = "<h3>New Contact Form Submission</h3>
                   <p><strong>Name:</strong> $name</p>
                   <p><strong>Email:</strong> $email</p>
                   <p><strong>Phone:</strong> $phone</p>
                   <p><strong>Subject:</strong> $subject</p>
                   <p><strong>Message:</strong></p>
                   <p>$message</p>";
    $mail->AltBody = "New Contact Form Submission\n\nName: $name\nEmail: $email\nPhone: $phone\nSubject: $subject\nMessage: $message";


    if ($mail->send()) {
        $response['success'] = true;
        $response['message'] = 'Message sent successfully!';
    } else {
        throw new Exception('Message could not be sent. Mailer Error: ' . $mail->ErrorInfo);
    }
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

header('Content-Type: application/json');
echo json_encode($response);
?>
