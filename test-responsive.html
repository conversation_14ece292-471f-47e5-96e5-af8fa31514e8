<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Test - Bottom Rounded Elements</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/custom.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-header {
            background: #eaf0fd;
            padding: 20px;
            text-align: center;
        }
        .viewport-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="viewport-info">
        <div>Viewport: <span id="viewport-width"></span>px</div>
        <div>Breakpoint: <span id="breakpoint"></span></div>
    </div>

    <div class="test-container">
        <div class="test-header">
            <h2>Responsive Test: Bottom Rounded Elements</h2>
            <p>Resize your browser window to test different breakpoints</p>
        </div>

        <!-- Bottom Rounded Elements -->
        <div class="hero-rounded-elements text-white">
            <div class="container py-2">
                <div class="row align-items-center justify-content-center g-2">
                    <!-- Setup Element -->
                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-6 mb-2 mb-lg-0">
                        <div class="rounded-element shadow-sm rounded-2 d-flex align-items-center" style="background: #134bc5;">
                            <div class="element-icon me-2">
                                <i class="bi-tools fs-5"></i>
                            </div>
                            <div class="element-content">
                                <h6 class="mb-0 fw-bold text-white">Setup</h6>
                                <small class="text-white-50">Quick installation</small>
                            </div>
                        </div>
                    </div>

                    <!-- Connectivity Element -->
                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-6 mb-2 mb-lg-0">
                        <div class="rounded-element shadow-sm rounded-2 d-flex align-items-center" style="background: #134bc5;">
                            <div class="element-icon me-2">
                                <i class="bi-wifi fs-5"></i>
                            </div>
                            <div class="element-content">
                                <h6 class="mb-0 fw-bold text-white">Connectivity</h6>
                                <small class="text-white-50">Wireless printing</small>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Element -->
                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-6 mb-2 mb-lg-0">
                        <div class="rounded-element shadow-sm rounded-2 d-flex align-items-center" style="background: #134bc5;">
                            <div class="element-icon me-2">
                                <i class="bi-speedometer2 fs-5"></i>
                            </div>
                            <div class="element-content">
                                <h6 class="mb-0 fw-bold text-white">Performance</h6>
                                <small class="text-white-50">Fast & reliable</small>
                            </div>
                        </div>
                    </div>

                    <!-- Support Element -->
                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-6 mb-2 mb-lg-0">
                        <div class="rounded-element shadow-sm rounded-2 d-flex align-items-center" style="background: #134bc5;">
                            <div class="element-icon me-2">
                                <i class="bi-headset fs-5"></i>
                            </div>
                            <div class="element-content">
                                <h6 class="mb-0 fw-bold text-white">Support</h6>
                                <small class="text-white-50">24/7 assistance</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="p-4">
            <h3>Breakpoint Information</h3>
            <ul>
                <li><strong>Desktop (992px+):</strong> 4 columns, larger padding, bigger text</li>
                <li><strong>Medium Tablets (768px-991px):</strong> 2x2 grid, medium padding</li>
                <li><strong>Small Tablets (576px-767px):</strong> 2x2 grid, smaller padding</li>
                <li><strong>Small Phones (575px and below):</strong> 2x2 grid, compact design</li>
                <li><strong>Extra Small (480px and below):</strong> Very compact, minimal padding</li>
            </ul>
        </div>
    </div>

    <script>
        function updateViewportInfo() {
            const width = window.innerWidth;
            document.getElementById('viewport-width').textContent = width;
            
            let breakpoint = '';
            if (width >= 1200) breakpoint = 'XL (1200px+)';
            else if (width >= 992) breakpoint = 'LG (992px+)';
            else if (width >= 768) breakpoint = 'MD (768px+)';
            else if (width >= 576) breakpoint = 'SM (576px+)';
            else if (width >= 480) breakpoint = 'XS (480px+)';
            else breakpoint = 'XXS (<480px)';
            
            document.getElementById('breakpoint').textContent = breakpoint;
        }

        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
    </script>
</body>
</html>
